<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_-19i4HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_-19i4XHkEfCAZJ6UDgwwcw" bindingContexts="_-1-x7nHkEfCAZJ6UDgwwcw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.c&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;real_mfcc_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/real_mfcc_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/real_mfcc_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights1.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights1.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights1.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_port.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_stdint.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_default_types.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stddef.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local_q15.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_utils.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_-19i4XHkEfCAZJ6UDgwwcw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_-19i4nHkEfCAZJ6UDgwwcw" label="%trimmedwindow.label.eclipseSDK" x="0" y="0" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1754150091289"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_-19i4nHkEfCAZJ6UDgwwcw" selectedElement="_-19i43HkEfCAZJ6UDgwwcw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_-19i43HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_-19i-XHkEfCAZJ6UDgwwcw">
        <children xsi:type="advanced:Perspective" xmi:id="_-19i5HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_-19i5XHkEfCAZJ6UDgwwcw" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:myplugintest.wizards.SampleNewWizard</tags>
          <tags>persp.newWizSC:myplugintest.wizards.PackageWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.profileActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_-19i5XHkEfCAZJ6UDgwwcw" selectedElement="_-19i6nHkEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_-19i5nHkEfCAZJ6UDgwwcw" elementId="topLeft" containerData="1417" selectedElement="_-19i53HkEfCAZJ6UDgwwcw">
              <children xsi:type="advanced:Placeholder" xmi:id="_-19i53HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_-1-LRnHkEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_-19i6HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_-1-LYnHkEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_-19i6XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_-1-LY3HkEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_-19i6nHkEfCAZJ6UDgwwcw" containerData="8583" selectedElement="_-19i83HkEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_-19i63HkEfCAZJ6UDgwwcw" containerData="6809" selectedElement="_-19i7HHkEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i7HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_-1-LDHHkEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_-19i7XHkEfCAZJ6UDgwwcw" elementId="topRight" containerData="2500" selectedElement="_-19i7nHkEfCAZJ6UDgwwcw">
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19i7nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" ref="_-1-LmHHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19i73HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_-1-Lm3HkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19i8HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_-1-LnHHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Mylyn</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19i8XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_-1-LnXHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19i8nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" ref="_-1-Ln3HkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:CMSIS Packs</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_-19i83HkEfCAZJ6UDgwwcw" elementId="bottom" containerData="3191" selectedElement="_-19i9nHkEfCAZJ6UDgwwcw">
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i9HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_-1-LZHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i9XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" ref="_-1-LbHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i9nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_-1-LbXHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i93HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" ref="_-1-Ll3HkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i-HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_-1-LnnHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_-19i-XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_-19i-nHkEfCAZJ6UDgwwcw" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.visualizer.view</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.xml.ui.perspective</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_-19i-nHkEfCAZJ6UDgwwcw" selectedElement="_-19jAXHkEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_-19i-3HkEfCAZJ6UDgwwcw" containerData="1453" selectedElement="_-19i_HHkEfCAZJ6UDgwwcw" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_-19i_HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_-19i_XHkEfCAZJ6UDgwwcw">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i_XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" ref="_-1-LoHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19i_nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_-1-LRnHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_-19i_3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jAHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_-1-MWHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_-19jAXHkEfCAZJ6UDgwwcw" containerData="8547" selectedElement="_-19jAnHkEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_-19jAnHkEfCAZJ6UDgwwcw" containerData="6383" selectedElement="_-19jA3HkEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jA3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="6500" ref="_-1-LDHHkEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_-19jBHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="3500" selectedElement="_-19jBXHkEfCAZJ6UDgwwcw">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jBXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" ref="_-1-L3XHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jBnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" ref="_-1-L_XHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" ref="_-1-MFnHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jCHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_-1-LmHHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jCXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_-1-Ll3HkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jCnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_-1-Lm3HkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jC3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" ref="_-1-MMXHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jDHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_-1-MM3HkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jDXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_-1-MNHHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jDnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" ref="_-1-MOHHkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_-19jD3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" ref="_-1-MY3HkEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_-19jEHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="3617" selectedElement="_-19jEXHkEfCAZJ6UDgwwcw">
                <tags>Debug</tags>
                <tags>General</tags>
                <tags>noFocus</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jEXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_-1-LbXHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jEnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" ref="_-1-LxHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jE3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_-1-LY3HkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jFHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_-1-L3HHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jFXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_-1-MMnHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_-1-LZHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jF3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_-1-MNXHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jGHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_-1-LnnHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jGXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_-1-MWXHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jGnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_-1-MYnHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jG3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" ref="_-1-MdHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jHHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" toBeRendered="false" ref="_-1-MiXHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_-19jHXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" ref="_-1-MjHHkEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
          <windows xsi:type="basic:TrimmedWindow" xmi:id="_-19jHnHkEfCAZJ6UDgwwcw" selectedElement="_-19jH3HkEfCAZJ6UDgwwcw" x="1805" y="140" width="1648" height="361">
            <children xsi:type="basic:PartStack" xmi:id="_-19jH3HkEfCAZJ6UDgwwcw" elementId="PartStack@523685f6" selectedElement="_-19jIHHkEfCAZJ6UDgwwcw">
              <children xsi:type="advanced:Placeholder" xmi:id="_-19jIHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" ref="_-1-MO3HkEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
            </children>
          </windows>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_-19jIXHkEfCAZJ6UDgwwcw" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_-19jInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_-1-LB3HkEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_-19jI3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_-1-LCHHkEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_-19jJHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_-1-LC3HkEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LCHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_-1-LCXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-LCnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LC3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_-1-LDHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" selectedElement="_-1-LDXHkEfCAZJ6UDgwwcw">
      <children xsi:type="basic:PartStack" xmi:id="_-1-LDXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_-1-LEnHkEfCAZJ6UDgwwcw">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <tags>active</tags>
        <children xsi:type="basic:Part" xmi:id="_-1-LDnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3300&quot; selectionTopPixel=&quot;2090&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LEnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="speaker_inference.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; partName=&quot;speaker_inference.c&quot; title=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;18&quot; selectionOffset=&quot;4011&quot; selectionTopPixel=&quot;3234&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>active</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; partName=&quot;nnom.c&quot; title=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;28295&quot; selectionTopPixel=&quot;22836&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LGnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_input.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; partName=&quot;nnom_input.c&quot; title=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4507&quot; selectionTopPixel=&quot;2662&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LHnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_tensor.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; partName=&quot;nnom_tensor.c&quot; title=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;573&quot; selectionTopPixel=&quot;330&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_conv2d.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; partName=&quot;nnom_conv2d.c&quot; title=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;18880&quot; selectionTopPixel=&quot;9020&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LJnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_local.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; partName=&quot;nnom_local.c&quot; title=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;51325&quot; selectionTopPixel=&quot;29458&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LKnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_activation.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; partName=&quot;nnom_activation.c&quot; title=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3598&quot; selectionTopPixel=&quot;3058&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LLnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_maxpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; partName=&quot;nnom_maxpool.c&quot; title=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;5121&quot; selectionTopPixel=&quot;3674&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LMnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_avgpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; partName=&quot;nnom_avgpool.c&quot; title=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4218&quot; selectionTopPixel=&quot;3121&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LNnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_flatten.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; partName=&quot;nnom_flatten.c&quot; title=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2137&quot; selectionTopPixel=&quot;1317&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LOnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_dense.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; partName=&quot;nnom_dense.c&quot; title=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;7044&quot; selectionTopPixel=&quot;4287&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LPnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_softmax.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; partName=&quot;nnom_softmax.c&quot; title=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2129&quot; selectionTopPixel=&quot;1364&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_-1-LQnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_output.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; partName=&quot;nnom_output.c&quot; title=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1263&quot; selectionTopPixel=&quot;704&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LRnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1754150091289&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_-1-LR3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-LW3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LYnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LY3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LZHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_-1-LZXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-LZ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LbHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LbXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_-1-LbnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-LcnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-Ll3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LmHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_-1-LmXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-LmnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-Lm3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LnHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LnXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LnnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-Ln3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
      <tags>View</tags>
      <tags>categoryTag:CMSIS Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LoHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-LoXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-LrHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-LxHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-LxXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-Lz3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-L3HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-L3XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-L3nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-L8nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-L_XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-L_nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MCHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MF3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MIXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MMXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MMnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MM3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MNHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MNXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MNnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MN3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MOHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view disassembly.syncActiveContext=&quot;true&quot; disassembly.trackExpression=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MOXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MOnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MO3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MPHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MUXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MWHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MWXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MWnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MXHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MYnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MY3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MZHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MbnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MdHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MdXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MfHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MiXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables &lt;1>" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_-1-MinHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-Mi3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_-1-MjHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view isPinned=&quot;false&quot;>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_-1-MjXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_-1-MnnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_-1-MxXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolControl" xmi:id="_-1-MxnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar" contributionURI="bundleclass://org.eclipse.launchbar.ui.controls/org.eclipse.launchbar.ui.controls.internal.LaunchBarControl"/>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-Mx3HkEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_-1-MyHHkEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-MyXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_-1-M0XHkEfCAZJ6UDgwwcw" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_-2AB2HHkEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-M13HkEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_-1-M2HHkEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-M2XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_-1-M23HkEfCAZJ6UDgwwcw" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_-2AAMnHkEfCAZJ6UDgwwcw"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_-1-M3HHkEfCAZJ6UDgwwcw" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_-2AAp3HkEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-M3XHkEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_-1-M3nHkEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NIXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NJ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NLHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NMnHkEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_-1-NM3HkEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NNHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_-1-NOnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_-2ABi3HkEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NP3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.editor.CEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NQHHkEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_-1-NQXHkEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NQnHkEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_-1-NQ3HkEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_-1-NRHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_-1-NR3HkEfCAZJ6UDgwwcw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_-1-NS3HkEfCAZJ6UDgwwcw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_-1-NUnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_-1-NU3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_-1-NVHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_-1-NVXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_-1-NXXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_-1-NXnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_-1-NX3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_-1-NYHHkEfCAZJ6UDgwwcw" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_-1-x7nHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-NYXHkEfCAZJ6UDgwwcw" keySequence="CTRL+1" command="_-1__-HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NYnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+L" command="_-2ACDXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NY3HkEfCAZJ6UDgwwcw" keySequence="CTRL+V" command="_-1__ZHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NZHHkEfCAZJ6UDgwwcw" keySequence="CTRL+A" command="_-2AAjHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NZXHkEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_-2AA1XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NZnHkEfCAZJ6UDgwwcw" keySequence="CTRL+X" command="_-2AAOnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NZ3HkEfCAZJ6UDgwwcw" keySequence="CTRL+Y" command="_-2AAp3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NaHHkEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_-2AAMnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NaXHkEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_UP" command="_-2AAsXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NanHkEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_DOWN" command="_-2ABWnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Na3HkEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_-1__ZHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NbHHkEfCAZJ6UDgwwcw" keySequence="ALT+F11" command="_-1__oHHkEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_-1-NbXHkEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_-1__hXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NbnHkEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_-2AA1XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nb3HkEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_UP" command="_-2AB7XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NcHHkEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_DOWN" command="_-2AAAHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NcXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F3" command="_-2AB4nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NcnHkEfCAZJ6UDgwwcw" keySequence="SHIFT+DEL" command="_-2AAOnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nc3HkEfCAZJ6UDgwwcw" keySequence="ALT+/" command="_-2ABsnHkEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_-1-NdHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_-1-x83HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-NdXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+CR" command="_-2AB4XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NdnHkEfCAZJ6UDgwwcw" keySequence="CTRL+BS" command="_-1__PXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nd3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Q" command="_-1__5XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NeHHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_-1__eXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NeXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+J" command="_-1__1nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NenHkEfCAZJ6UDgwwcw" keySequence="CTRL++" command="_-2ABPHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Ne3HkEfCAZJ6UDgwwcw" keySequence="CTRL+-" command="_-2AAdXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NfHHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_-2ABe3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NfXHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+V" command="_-1__kHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NfnHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+J" command="_-1__8XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nf3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+A" command="_-2AA83HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NgHHkEfCAZJ6UDgwwcw" keySequence="CTRL+J" command="_-1__i3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NgXHkEfCAZJ6UDgwwcw" keySequence="CTRL+L" command="_-2ABxXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NgnHkEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_-1__lHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Ng3HkEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_-2ABPHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NhHHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+/" command="_-2ACHnHkEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_-1-NhXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Y" command="_-1__NHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NhnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+DEL" command="_-2ABt3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nh3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+X" command="_-2AA3XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NiHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Y" command="_-2AAc3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NiXHkEfCAZJ6UDgwwcw" keySequence="CTRL+DEL" command="_-2AALnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NinHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_-2ACa3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Ni3HkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_-2ABZXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NjHHkEfCAZJ6UDgwwcw" keySequence="SHIFT+END" command="_-2AAe3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NjXHkEfCAZJ6UDgwwcw" keySequence="SHIFT+HOME" command="_-2AAYnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NjnHkEfCAZJ6UDgwwcw" keySequence="END" command="_-2AB-XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nj3HkEfCAZJ6UDgwwcw" keySequence="INSERT" command="_-2ABFHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NkHHkEfCAZJ6UDgwwcw" keySequence="F2" command="_-2AAAnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NkXHkEfCAZJ6UDgwwcw" keySequence="HOME" command="_-2ACGXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NknHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_UP" command="_-2ACRXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nk3HkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_DOWN" command="_-2AAj3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NlHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+INSERT" command="_-1__wXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NlXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_-2AAfXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NlnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_-1__yHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nl3HkEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_-2AB3XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NmHHkEfCAZJ6UDgwwcw" keySequence="CTRL+END" command="_-2ABZ3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NmXHkEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_UP" command="_-1__sHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NmnHkEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_DOWN" command="_-2ACe3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nm3HkEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_-2AAz3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NnHHkEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_RIGHT" command="_-1__4nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NnXHkEfCAZJ6UDgwwcw" keySequence="CTRL+HOME" command="_-1__Y3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NnnHkEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_-2ABenHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nn3HkEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_ADD" command="_-2ACNHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NoHHkEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_-2AB33HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NoXHkEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_DIVIDE" command="_-1__snHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NonHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_-2ABg3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-No3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_-2ABFXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NpHHkEfCAZJ6UDgwwcw" keySequence="SHIFT+CR" command="_-2ACFnHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-NpXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_-1-yCHHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-NpnHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+C" command="_-2ACfnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Np3HkEfCAZJ6UDgwwcw" keySequence="CTRL+TAB" command="_-2ACd3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NqHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_-2AAV3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NqXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_-2AAE3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NqnHkEfCAZJ6UDgwwcw" keySequence="CTRL+7" command="_-1__Z3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nq3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_-2ABJ3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NrHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+N" command="_-1__4XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NrXHkEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_-1__Z3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NrnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+O" command="_-2AAS3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nr3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_-2AB43HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NsHHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+S" command="_-2ACWXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NsXHkEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_-2ABmXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NsnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_-1__Z3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Ns3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_-2ACeHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NtHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_-1__OHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NtXHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_-1__-3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NtnHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_-2ABAHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nt3HkEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_-2ABanHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NuHHkEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_-1__03HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NuXHkEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_-2AA5nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NunHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_-2ACMXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nu3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_-2ABvHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NvHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_-1__h3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NvXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+T" command="_-2ABn3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NvnHkEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_-2ACUHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nv3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+L" command="_-2AAkXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NwHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+M" command="_-1__WnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NwXHkEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_-2ABmXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NwnHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+O" command="_-2AAQHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nw3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Z" command="_-2AB0HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NxHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_-2AA_HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NxXHkEfCAZJ6UDgwwcw" keySequence="F3" command="_-2ACg3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NxnHkEfCAZJ6UDgwwcw" keySequence="F4" command="_-2ACLHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Nx3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_-2ABwXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NyHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_-2ABXXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NyXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_-2AAaHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NynHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_-2ACeXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-Ny3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_-2ABs3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NzHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_-2ACCHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NzXHkEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_-2AA_XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-NznHkEfCAZJ6UDgwwcw" keySequence="SHIFT+TAB" command="_-2ABKHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-Nz3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_-1-yFHHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-N0HHkEfCAZJ6UDgwwcw" keySequence="CTRL+CR" command="_-2AAIXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N0XHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_-2AAwnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N0nHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_-2AAfHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N03HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_-2ABcnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N1HHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_-2AAdnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N1XHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_-2ABSXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N1nHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_-2AAJnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N13HkEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_-1__pXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N2HHkEfCAZJ6UDgwwcw" keySequence="INSERT" command="_-2AAcXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N2XHkEfCAZJ6UDgwwcw" keySequence="F4" command="_-1__gXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N2nHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_-2ABoXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N23HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_-2AAeHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-N3HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" bindingContext="_-1-x73HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-N3XHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+T" command="_-1__hHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N3nHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+L" command="_-2ABB3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N33HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q O" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N4HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_-1-N4XHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+B" command="_-2ABVHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N4nHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+R" command="_-2ACgHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N43HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Q" command="_-2ABSnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N5HHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+S" command="_-2ABNnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N5XHkEfCAZJ6UDgwwcw" keySequence="CTRL+3" command="_-2AAAXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N5nHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q S" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N53HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_-1-N6HHkEfCAZJ6UDgwwcw" keySequence="CTRL+6" command="_-2ACFXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N6XHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q V" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N6nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_-1-N63HkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+G" command="_-2ABQnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N7HHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+W" command="_-2AAOXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N7XHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q H" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N7nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_-1-N73HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+K" command="_-1__rHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N8HHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q K" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N8XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_-1-N8nHkEfCAZJ6UDgwwcw" keySequence="CTRL+," command="_-1__aHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N83HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q L" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N9HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_-1-N9XHkEfCAZJ6UDgwwcw" keySequence="CTRL+." command="_-2ACR3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N9nHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_-2AAY3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N93HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+B" command="_-1__rXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N-HHkEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_-1__hnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N-XHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+T" command="_-2AA0XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N-nHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+E" command="_-1__vXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-N-3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q X" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N_HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_-1-N_XHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Y" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-N_nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_-1-N_3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Z" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-OAHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_-1-OAXHkEfCAZJ6UDgwwcw" keySequence="CTRL+P" command="_-2AB2HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-OAnHkEfCAZJ6UDgwwcw" keySequence="CTRL+Q" command="_-2AB6HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-OA3HkEfCAZJ6UDgwwcw" keySequence="CTRL+S" command="_-2AAd3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-OBHHkEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_-2AArHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xAHHkEfCAZJ6UDgwwcw" keySequence="CTRL+H" command="_-2ABsXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xAXHkEfCAZJ6UDgwwcw" keySequence="CTRL+K" command="_-2ABV3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xAnHkEfCAZJ6UDgwwcw" keySequence="CTRL+M" command="_-2ABrHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xA3HkEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_-2ACW3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xBHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+?" command="_-2AAFHHkEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_-1-xBXHkEfCAZJ6UDgwwcw" keySequence="CTRL+B" command="_-1__bXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xBnHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q B" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-xB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_-1-xCHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q C" command="_-2ABSnHkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-xCXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_-1-xCnHkEfCAZJ6UDgwwcw" keySequence="CTRL+E" command="_-2AAK3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xC3HkEfCAZJ6UDgwwcw" keySequence="CTRL+F" command="_-1__m3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xDHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+W" command="_-2ACRHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xDXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+H" command="_-2AAI3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xDnHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+N" command="_-2AANnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xD3HkEfCAZJ6UDgwwcw" keySequence="CTRL+_" command="_-2AAF3HkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-xEHHkEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_-1-xEXHkEfCAZJ6UDgwwcw" keySequence="CTRL+{" command="_-2AAF3HkEfCAZJ6UDgwwcw">
      <parameters xmi:id="_-1-xEnHkEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_-1-xE3HkEfCAZJ6UDgwwcw" keySequence="SHIFT+F9" command="_-2AASnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xFHHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_-1__iXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xFXHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_-2AATXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xFnHkEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_-2AAlnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xF3HkEfCAZJ6UDgwwcw" keySequence="ALT+F7" command="_-2AA-nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xGHHkEfCAZJ6UDgwwcw" keySequence="F9" command="_-2AAWnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xGXHkEfCAZJ6UDgwwcw" keySequence="F11" command="_-2ACKHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xGnHkEfCAZJ6UDgwwcw" keySequence="F12" command="_-2ABtXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xG3HkEfCAZJ6UDgwwcw" keySequence="F2" command="_-1__bHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xHHHkEfCAZJ6UDgwwcw" keySequence="F5" command="_-2AAVHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xHXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F7" command="_-2ACKnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xHnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F8" command="_-2AAFnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xH3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F9" command="_-2AAhnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xIHHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_LEFT" command="_-2AB6HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xIXHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_RIGHT" command="_-1__tXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xInHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F12" command="_-1__TXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xI3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F4" command="_-2AAOXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xJHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F6" command="_-2ABO3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xJXHkEfCAZJ6UDgwwcw" keySequence="CTRL+F7" command="_-2AA1nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xJnHkEfCAZJ6UDgwwcw" keySequence="CTRL+F8" command="_-1__-XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xJ3HkEfCAZJ6UDgwwcw" keySequence="CTRL+F9" command="_-1__wHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xKHHkEfCAZJ6UDgwwcw" keySequence="CTRL+F11" command="_-2AB_HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xKXHkEfCAZJ6UDgwwcw" keySequence="CTRL+F12" command="_-1__r3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xKnHkEfCAZJ6UDgwwcw" keySequence="CTRL+F4" command="_-2AArHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xK3HkEfCAZJ6UDgwwcw" keySequence="CTRL+F6" command="_-1__o3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xLHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F7" command="_-2ABaXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xLXHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_-2ABInHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xLnHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_-2ACZ3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xL3HkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_-2ABEHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xMHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_-2ABNXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xMXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_-2AAGXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xMnHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+F12" command="_-2ACNnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xM3HkEfCAZJ6UDgwwcw" keySequence="DEL" command="_-1__qHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xNHHkEfCAZJ6UDgwwcw" keySequence="ALT+?" command="_-2AAFHHkEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_-1-xNXHkEfCAZJ6UDgwwcw" keySequence="ALT+-" command="_-2AA6XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xNnHkEfCAZJ6UDgwwcw" keySequence="ALT+CR" command="_-2ABnHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xN3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_-1-x9nHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xOHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_-2AAbnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xOXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_-2AB-HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xOnHkEfCAZJ6UDgwwcw" keySequence="F3" command="_-2AB6XHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xO3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_-1-x_HHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xPHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_-2AA9HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xPXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_-2ACfHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xPnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_-2ACEXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xP3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_-2ACV3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xQHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+>" command="_-2ABx3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xQXHkEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_-2AB2nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xQnHkEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_-2ABAnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xQ3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_-2AA6HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xRHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_-2ABQ3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xRXHkEfCAZJ6UDgwwcw" keySequence="F3" command="_-2AA8XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xRnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_-1__0nHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xR3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_-2AAnnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xSHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_-2ABYXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xSXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_-2ABdnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xSnHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_-1__e3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xS3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_-2ABKXHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xTHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" bindingContext="_-1-yC3HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xTXHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_-1__eXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xTnHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_-2ABe3HkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xT3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_-1-yFXHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xUHHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_-2AAE3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xUXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_-2ABJ3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xUnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_-1__OHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xU3HkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_-1__-3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xVHHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_-2ABAHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xVXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_-2ABvHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xVnHkEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_-2ACUHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xV3HkEfCAZJ6UDgwwcw" keySequence="F3" command="_-2ACg3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xWHHkEfCAZJ6UDgwwcw" keySequence="F4" command="_-2ACLHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xWXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_-1-x8XHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xWnHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+V" command="_-2AAbHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xW3HkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_-2ABkXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xXHHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_-1__O3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xXXHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_-2ACJnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xXnHkEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_-2AAbHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xX3HkEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_-2ABkXHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xYHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_-1-x93HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xYXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+M" command="_-1__V3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xYnHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_-2AAwnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xY3HkEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_-2ACPXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xZHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_-2AAfHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xZXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_-2AAP3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xZnHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_-2ABcnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xZ3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_-2AAdnHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xaHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_-1-x-HHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xaXHkEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_-2ABT3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xanHkEfCAZJ6UDgwwcw" keySequence="F3" command="_-2ABL3HkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xa3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_-1-yDXHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xbHHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+M" command="_-2AAinHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xbXHkEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+N" command="_-2ACN3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xbnHkEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_-2AABXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xb3HkEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_-2ABGnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xcHHkEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_-2ABP3HkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xcXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_-1-yDnHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xcnHkEfCAZJ6UDgwwcw" keySequence="CTRL+R" command="_-2AA23HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xc3HkEfCAZJ6UDgwwcw" keySequence="F7" command="_-2ACTHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xdHHkEfCAZJ6UDgwwcw" keySequence="F8" command="_-2ABD3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xdXHkEfCAZJ6UDgwwcw" keySequence="F5" command="_-1__eHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xdnHkEfCAZJ6UDgwwcw" keySequence="F6" command="_-2AAfnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xd3HkEfCAZJ6UDgwwcw" keySequence="CTRL+F2" command="_-2ABuHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xeHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_-1-yD3HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xeXHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+," command="_-2AB63HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xenHkEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+." command="_-2ABqXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xe3HkEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_-2ABqnHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xfHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_-1-x9HHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xfXHkEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_-2AAmnHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xfnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_-1-x-nHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xf3HkEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_-1__XnHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xgHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_-1-yFnHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xgXHkEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_-2AACXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xgnHkEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_-1__j3HkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xg3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_-1-yEnHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xhHHkEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_-1__pHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xhXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" bindingContext="_-1-yHnHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xhnHkEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_-2AA9nHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xh3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_-1-yEHHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xiHHkEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_-2ACWHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xiXHkEfCAZJ6UDgwwcw" keySequence="HOME" command="_-2AABHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xinHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" bindingContext="_-1-yCXHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xi3HkEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_-2ACQHHkEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_-1-xjHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_-1-yEXHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xjXHkEfCAZJ6UDgwwcw" keySequence="SHIFT+F7" command="_-2ABLnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xjnHkEfCAZJ6UDgwwcw" keySequence="SHIFT+F8" command="_-2ABp3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xj3HkEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_-2AAf3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xkHHkEfCAZJ6UDgwwcw" keySequence="SHIFT+F6" command="_-1__cHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xkXHkEfCAZJ6UDgwwcw" keySequence="CTRL+F5" command="_-2ACIHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xknHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_-1-yGXHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xk3HkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_-2ABa3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xlHHkEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_-2ABf3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xlXHkEfCAZJ6UDgwwcw" keySequence="F3" command="_-2ACg3HkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xlnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_-1-x-XHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xl3HkEfCAZJ6UDgwwcw" keySequence="F1" command="_-1__RXHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xmHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_-1-yF3HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xmXHkEfCAZJ6UDgwwcw" keySequence="F2" command="_-1__qnHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xmnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_-1-x9XHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xm3HkEfCAZJ6UDgwwcw" keySequence="F3" command="_-2ACg3HkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xnHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_-1-yG3HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xnXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_-2AATnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xnnHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_-1__VXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xn3HkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_-2AAxHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xoHHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_-2ABHXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xoXHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+HOME" command="_-2ACQ3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xonHkEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+END" command="_-2AAKnHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xo3HkEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_-2ABsHHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xpHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_-1-yDHHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xpXHkEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_-2ABM3HkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xpnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" bindingContext="_-1-x-3HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xp3HkEfCAZJ6UDgwwcw" keySequence="ESC CTRL+F" command="_-2ABCnHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xqHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" bindingContext="_-1-yHXHkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xqXHkEfCAZJ6UDgwwcw" keySequence="Z" command="_-2AAknHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xqnHkEfCAZJ6UDgwwcw" keySequence="+" command="_-1__OXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xq3HkEfCAZJ6UDgwwcw" keySequence="-" command="_-1__fXHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xrHHkEfCAZJ6UDgwwcw" keySequence="/" command="_-1__2XHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xrXHkEfCAZJ6UDgwwcw" keySequence="S" command="_-2AAPHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xrnHkEfCAZJ6UDgwwcw" keySequence="W" command="_-2AAn3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xr3HkEfCAZJ6UDgwwcw" keySequence="A" command="_-2AAB3HkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xsHHkEfCAZJ6UDgwwcw" keySequence="D" command="_-2AApHHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xsXHkEfCAZJ6UDgwwcw" keySequence="=" command="_-1__OXHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xsnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_-1-yE3HkEfCAZJ6UDgwwcw">
    <bindings xmi:id="_-1-xs3HkEfCAZJ6UDgwwcw" keySequence="ALT+Y" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xtHHkEfCAZJ6UDgwwcw" keySequence="ALT+A" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xtXHkEfCAZJ6UDgwwcw" keySequence="ALT+B" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xtnHkEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xt3HkEfCAZJ6UDgwwcw" keySequence="ALT+D" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xuHHkEfCAZJ6UDgwwcw" keySequence="ALT+E" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xuXHkEfCAZJ6UDgwwcw" keySequence="ALT+F" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xunHkEfCAZJ6UDgwwcw" keySequence="ALT+G" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xu3HkEfCAZJ6UDgwwcw" keySequence="ALT+P" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xvHHkEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xvXHkEfCAZJ6UDgwwcw" keySequence="ALT+S" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xvnHkEfCAZJ6UDgwwcw" keySequence="ALT+T" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xv3HkEfCAZJ6UDgwwcw" keySequence="ALT+V" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xwHHkEfCAZJ6UDgwwcw" keySequence="ALT+W" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xwXHkEfCAZJ6UDgwwcw" keySequence="ALT+H" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xwnHkEfCAZJ6UDgwwcw" keySequence="ALT+L" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_-1-xw3HkEfCAZJ6UDgwwcw" keySequence="ALT+N" command="_-2AA5HHkEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_-1-xxHHkEfCAZJ6UDgwwcw" bindingContext="_-1-yH3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xxXHkEfCAZJ6UDgwwcw" bindingContext="_-1-yIHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xxnHkEfCAZJ6UDgwwcw" bindingContext="_-1-yIXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xx3HkEfCAZJ6UDgwwcw" bindingContext="_-1-yInHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xyHHkEfCAZJ6UDgwwcw" bindingContext="_-1-yI3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xyXHkEfCAZJ6UDgwwcw" bindingContext="_-1-yJHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xynHkEfCAZJ6UDgwwcw" bindingContext="_-1-yJXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xy3HkEfCAZJ6UDgwwcw" bindingContext="_-1-yJnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xzHHkEfCAZJ6UDgwwcw" bindingContext="_-1-yJ3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xzXHkEfCAZJ6UDgwwcw" bindingContext="_-1-yKHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xznHkEfCAZJ6UDgwwcw" bindingContext="_-1-yKXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-xz3HkEfCAZJ6UDgwwcw" bindingContext="_-1-yKnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x0HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yK3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x0XHkEfCAZJ6UDgwwcw" bindingContext="_-1-yLHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x0nHkEfCAZJ6UDgwwcw" bindingContext="_-1-yLXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x03HkEfCAZJ6UDgwwcw" bindingContext="_-1-yLnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x1HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yL3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x1XHkEfCAZJ6UDgwwcw" bindingContext="_-1-yMHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x1nHkEfCAZJ6UDgwwcw" bindingContext="_-1-yMXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x13HkEfCAZJ6UDgwwcw" bindingContext="_-1-yMnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x2HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yM3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x2XHkEfCAZJ6UDgwwcw" bindingContext="_-1-yNHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x2nHkEfCAZJ6UDgwwcw" bindingContext="_-1-yNXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x23HkEfCAZJ6UDgwwcw" bindingContext="_-1-yNnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x3HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yN3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x3XHkEfCAZJ6UDgwwcw" bindingContext="_-1-yOHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x3nHkEfCAZJ6UDgwwcw" bindingContext="_-1-yOXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x33HkEfCAZJ6UDgwwcw" bindingContext="_-1-yOnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x4HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yO3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x4XHkEfCAZJ6UDgwwcw" bindingContext="_-1-yPHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x4nHkEfCAZJ6UDgwwcw" bindingContext="_-1-yPXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x43HkEfCAZJ6UDgwwcw" bindingContext="_-1-yPnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x5HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yP3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x5XHkEfCAZJ6UDgwwcw" bindingContext="_-1-yQHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x5nHkEfCAZJ6UDgwwcw" bindingContext="_-1-yQXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x53HkEfCAZJ6UDgwwcw" bindingContext="_-1-yQnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x6HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yQ3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x6XHkEfCAZJ6UDgwwcw" bindingContext="_-1-yRHHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x6nHkEfCAZJ6UDgwwcw" bindingContext="_-1-yRXHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x63HkEfCAZJ6UDgwwcw" bindingContext="_-1-yRnHkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x7HHkEfCAZJ6UDgwwcw" bindingContext="_-1-yR3HkEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_-1-x7XHkEfCAZJ6UDgwwcw" bindingContext="_-1-ySHHkEfCAZJ6UDgwwcw"/>
  <rootContext xmi:id="_-1-x7nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_-1-x73HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_-1-x8HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_-1-x8XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_-1-x8nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_-1-x83HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_-1-x9HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_-1-x9XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_-1-x9nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_-1-x93HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_-1-x-HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_-1-x-XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_-1-x-nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_-1-x-3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" name="ChangeLog Editor"/>
        <children xmi:id="_-1-x_HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_-1-x_XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_-1-x_nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_-1-x_3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_-1-yAHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_-1-yAXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_-1-yAnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_-1-yA3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_-1-yBHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_-1-yBXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_-1-yBnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_-1-yB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
        </children>
        <children xmi:id="_-1-yCHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_-1-yCXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_-1-yCnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_-1-yC3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_-1-yDHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_-1-yDXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_-1-yDnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_-1-yD3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_-1-yEHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_-1-yEXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_-1-yEnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_-1-yE3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_-1-yFHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_-1-yFXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_-1-yFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_-1-yF3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_-1-yGHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_-1-yGXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_-1-yGnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_-1-yG3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_-1-yHHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_-1-yHXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" name="In Time-Based View"/>
  <rootContext xmi:id="_-1-yHnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" name="In Time Graph"/>
  <rootContext xmi:id="_-1-yH3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_-1-yIHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_-1-yIXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_-1-yInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_-1-yI3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_-1-yJHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_-1-yJXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_-1-yJnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.actionSet" name="Auto::org.eclipse.mylyn.cdt.ui.actionSet"/>
  <rootContext xmi:id="_-1-yJ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_-1-yKHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_-1-yKXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_-1-yKnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_-1-yK3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_-1-yLHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_-1-yLXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_-1-yLnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_-1-yL3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_-1-yMHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_-1-yMXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_-1-yMnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_-1-yM3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_-1-yNHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_-1-yNXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_-1-yNnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_-1-yN3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_-1-yOHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_-1-yOXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_-1-yOnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_-1-yO3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_-1-yPHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_-1-yPXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_-1-yPnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_-1-yP3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_-1-yQHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_-1-yQXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_-1-yQnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_-1-yQ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_-1-yRHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_-1-yRXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_-1-yRnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_-1-yR3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_-1-ySHHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_-1-ySXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ySnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yS3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yTHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yTXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yTnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yT3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yUHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yUXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yUnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yU3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yVHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.testsrunner.resultsview" label="C/C++ Unit" iconURI="platform:/plugin/org.eclipse.cdt.testsrunner/icons/eview16/cppunit.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.testsrunner.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.testsrunner"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yVXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yVnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yV3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yWHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yWXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yWnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yW3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yXHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yXXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yXnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yX3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yYHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yYXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yYnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yY3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yZHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yZXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yZnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yZ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yaHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yaXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yanHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ya3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/hardware_chip.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ybHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/board.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ybXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/info_obj.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ybnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.PacksView" label="CMSIS Packs" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/packages.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yb3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/outline_co.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ycHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ycXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.dataviewers.charts.view.ChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.dataviewers.charts"/>
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ycnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gcov.view" label="gcov" iconURI="platform:/plugin/org.eclipse.linuxtools.gcov.core/icons/toggle.gif" tooltip="" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gcov.view.CovView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gcov.core"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yc3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gprof.view.GmonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gprof"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ydHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" tooltip="" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.valgrind.ui.ValgrindViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.valgrind.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ydXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ydnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yd3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yeHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yeXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yenHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ye3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yfHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yfXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yfnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yf3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ygHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ygXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ygnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.counters.ui.views.countersview" label="Counters" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.counters.ui/icons/counter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.counters.ui.views.CounterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.counters.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yg3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.graph.ui.criticalpath.view.criticalpathview" label="Critical Flow View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.graph.ui/icons/eview16/critical-path.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.graph.ui.criticalpath.view.CriticalPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.graph.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yhHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.lami.views.reportview" label="Analysis Report" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.provisional.analysis.lami.ui.views.LamiReportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.lami.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yhXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yhnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.scatter" label="Sched_Wakeup/Switch Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yh3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yiHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname" label="Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yiXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yinHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority" label="Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yi3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yjHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.density" label="Sched_Wakeup/Switch Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yjXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.controlflow.ControlFlowView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yjnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.resources.ResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yj3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/cpu-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.cpuusage.CpuUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ykHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ykXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.scatter" label="System Call Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yknHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yk3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.density" label="System Call Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ylHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.kernelmemoryusageview" label="Kernel Memory Usage View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.kernelmemoryusage.KernelMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ylXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.diskioactivity" label="Disk I/O Activity" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.io.diskioactivity.DiskIOActivityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ylnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Flame Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.profiling.ui.views.flamechart.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yl3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.callgraphDensity" label="Function Durations Distribution" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.CallGraphDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ymHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.flamegraph.flamegraphView" label="Flame Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ymXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.statistics.callgraphstatistics" label="Function Duration Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.statistics.CallGraphStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ymnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table" label="Segment Store Table" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ym3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics" label="Descriptive Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ynHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2" label="Segments vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ynXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ynnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yn3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Matches Scatter Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yoHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.control.ui.views.ControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.control.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yoXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.ust.ui.views.memusage.UstMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yonHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yo3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ypHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/ganttxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.timegraph.XmlTimeGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ypXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/linechartxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.xychart.XmlXYView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ypnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latencytable" label="Latency Table" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternLatencyTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yp3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.scattergraph" label="Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/scatter.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternScatterGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yqHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.density" label="Latency vs Count" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/density.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yqXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.statistics" label="Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yqnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.timechart.TimeChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yq3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statesystem.TmfStateSystemExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yrHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yrXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yrnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.uml2sd.SDView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yr3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.statistics.TmfStatisticsViewImpl"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ysHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ysXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.views.eventdensity" label="Event Density" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.eventdensity.EventDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ysnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.synchronization.TmfSynchronizationView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ys3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ytHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ytXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ytnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yt3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yuHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yuXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yunHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yu3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yvHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yvXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yvnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yv3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ywHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ywXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_-1-ywnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_-1-yw3HkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.tools.trace.ui.TabTrace" label="Trace" iconURI="platform:/plugin/org.riscvstudio.ide.tools.trace/icons/trace/trace.png" tooltip="" category="RV Trace" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.riscvstudio.ide.tools.trace.ui.TabTrace"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.riscvstudio.ide.tools.trace"/>
    <tags>View</tags>
    <tags>categoryTag:RV Trace</tags>
  </descriptors>
  <trimContributions xmi:id="_-1__IHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_-1__IXHkEfCAZJ6UDgwwcw" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_-1__InHkEfCAZJ6UDgwwcw" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_-1__I3HkEfCAZJ6UDgwwcw" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_-1__M3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__NHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__NXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__NnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_-1__N3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_-2ADEnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__OHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__OXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin.selection" commandName="Zoom in (selection)" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__OnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_-*********************"/>
  <commands xmi:id="_-1__O3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_-2ADK3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__PHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__PXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__PnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__P3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__QHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__QXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__QnHkEfCAZJ6UDgwwcw" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_-1__Q3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__RHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_-*********************"/>
  <commands xmi:id="_-1__RXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__RnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_-2ADMXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__R3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__SHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__SXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__SnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__S3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__THHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__TXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__TnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__T3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugRemoteExecutable" commandName="Debug Remote Executable" description="Debug a Remote executable" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__UHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_-2ADFnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__UXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__UnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableLogger" commandName="Disable Logger" description="Disable Logger" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__U3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__VHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__VXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_-2ADMHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__VnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.analysis.xml.ui.managexmlanalyses" commandName="Manage XML analyses..." description="Manage XML files containing analysis information" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__V3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_-2ADHnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__WHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__WXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__WnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__W3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__XHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__XXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__XnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__X3HkEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_-2ADBnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__YHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__YXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__YnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__Y3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__ZHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__ZXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__ZnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__Z3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__aHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__aXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_-*********************"/>
  <commands xmi:id="_-1__anHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.open_as_experiment" commandName="Open As Experiment..." description="Open selected traces as an experiment" category="_-2ADCHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__a3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_-1__bHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__bXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__bnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__b3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__cHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_-2ADM3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__cXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.save" commandName="Save..." description="Save session(s)" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__cnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__c3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugAttachedExecutable" commandName="Debug Attached Executable" description="Debug an attached executable" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__dHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__dXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__dnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__d3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_-2ADMnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__eHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__eXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.actions.KeyActionCommand" commandName="Insert ChangeLog entry" description="Insert a ChangeLog entry" category="_-2ADBHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__enHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__e3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__fHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_-*********************"/>
  <commands xmi:id="_-1__fXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout.selection" commandName="Zoom out (selection)" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__fnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__f3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__gHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_-1__gXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__gnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__g3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__hHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_-2ADJHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__hXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__hnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_-*********************"/>
  <commands xmi:id="_-1__h3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__iHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__iXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__inHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__i3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__jHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_-*********************"/>
  <commands xmi:id="_-1__jXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__jnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_-2ADAHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__j3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_-*********************"/>
  <commands xmi:id="_-1__kHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.prepareCommit" commandName="Prepare Commit" description="Copies latest changelog entry to clipboard" category="_-2ADBHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__kXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_-*********************"/>
  <commands xmi:id="_-1__knHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.updateCommand" commandName="Refresh" category="_-2ADDHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__k3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__lHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__lXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__lnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.remove" commandName="Remove" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__l3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__mHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gdbtrace.ui.command.project.trace.selectexecutable" commandName="Select Trace Executable..." description="Select executable binary file for a GDB trace" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__mXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__mnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__m3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__nHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__nXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_-*********************"/>
  <commands xmi:id="_-1__nnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__n3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_-1__oHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__oXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__onHkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_-2ADG3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__o3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__pHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__pXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__pnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__p3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_-*********************"/>
  <commands xmi:id="_-1__qHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__qXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__qnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_-*********************"/>
  <commands xmi:id="_-1__q3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_-*********************"/>
  <commands xmi:id="_-1__rHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__rXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__rnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_-2ADLnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__r3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__sHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__sXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__snHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__s3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__tHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__tXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__tnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__t3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_-2ADF3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__uHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__uXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__unHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_-2ADCHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__u3HkEfCAZJ6UDgwwcw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_-1__vHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__vXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__vnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__v3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__wHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__wXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__wnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_-*********************"/>
  <commands xmi:id="_-1__w3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__xHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__xXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__xnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__x3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__yHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__yXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__ynHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_-2ADAHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__y3HkEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_-1__zHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__zXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__znHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_-*********************"/>
  <commands xmi:id="_-1__z3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__0HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_-*********************"/>
  <commands xmi:id="_-1__0XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__0nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__03HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__1HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_-*********************"/>
  <commands xmi:id="_-1__1XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__1nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__13HkEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__2HHkEfCAZJ6UDgwwcw" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_-1__2XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.filter" commandName="Filter Time Graph events" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__2nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_-*********************"/>
  <commands xmi:id="_-1__23HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__3HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_-2ADAnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__3XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_-2ADInHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__3nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__33HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__4HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_-2ADLXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__4XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__4nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__43HkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__5HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.logger" commandName="Enable Logger..." description="Assign Logger to Session and Enable Logger" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__5XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__5nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__53HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_-*********************"/>
  <commands xmi:id="_-1__6HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.remote.ui.command.fetchlog" commandName="Fetch Remote Traces..." category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__6XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_-*********************"/>
  <commands xmi:id="_-1__6nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__63HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__7HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__7XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_-*********************"/>
  <commands xmi:id="_-1__7nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_-2ADJ3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1__73HkEfCAZJ6UDgwwcw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_-1__8HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__8XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__8nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__83HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__9HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_-2ADAnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__9XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_-2ADEnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__9nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__93HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_-*********************"/>
  <commands xmi:id="_-1__-HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__-XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__-nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1__-3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1___HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1___XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-1___nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-1___3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_-2AAAHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAAXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAAnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAA3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_-2ADHnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AABHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AABXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AABnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_-*********************"/>
  <commands xmi:id="_-2AAB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.left" commandName="Left" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AACHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_-*********************"/>
  <commands xmi:id="_-2AACXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_-*********************"/>
  <commands xmi:id="_-2AACnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_-2ADAHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAC3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AADHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AADXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_-2ADCnHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AADnHkEfCAZJ6UDgwwcw" elementId="url" name="URL"/>
    <parameters xmi:id="_-2AAD3HkEfCAZJ6UDgwwcw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_-2AAEHHkEfCAZJ6UDgwwcw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_-2AAEXHkEfCAZJ6UDgwwcw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_-2AAEnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAE3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAFHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAFXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAF3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_-2ADCnHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAGHHkEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_-2AAGXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAGnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAG3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAHHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAHXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAHnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAH3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAIHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAIXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_-*********************"/>
  <commands xmi:id="_-2AAI3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAJHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAJXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_add" commandName="Add External Analysis" description="Add External Analysis" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAJnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAJ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAKHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAKXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAKnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_-2ADMHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAK3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AALHHkEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_-2ADBnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AALXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AALnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAL3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAMHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_-*********************"/>
  <commands xmi:id="_-2AAMXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAMnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAM3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AANHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_-2ADGXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AANXHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_SDK_Documentation" commandName="Nuclei SDK Documentation"/>
  <commands xmi:id="_-2AANnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAN3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAOHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAOXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAOnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAO3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAPHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout" commandName="Zoom out (mouse position)" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAPXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_-*********************"/>
  <commands xmi:id="_-2AAPnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_-*********************"/>
  <commands xmi:id="_-2AAP3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_-2ADHnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAQHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAQXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAQnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAQ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AARHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_-*********************"/>
  <commands xmi:id="_-2AARXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_-2ADCHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AARnHkEfCAZJ6UDgwwcw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_-2AAR3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.report_delete" commandName="Delete Report" description="Delete this report from the project" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AASHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_-2ADIXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AASXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_-*********************"/>
  <commands xmi:id="_-2AASnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAS3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AATHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AATXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AATnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_-2ADMHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAT3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_-2ADLXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAUHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAUXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAUnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_-*********************"/>
  <commands xmi:id="_-2AAU3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_-2ADLnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAVHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAVXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_-*********************"/>
  <commands xmi:id="_-2AAVnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_-*********************"/>
  <commands xmi:id="_-2AAV3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAWHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_-2ADEnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAWXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAWnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAW3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAXHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAXXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAXnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAX3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAYHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAYXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAYnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAY3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog" commandName="Prepare Changelog" description="Prepares Changelog" category="_-2ADBHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAZHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAZXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAZnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAZ3HkEfCAZJ6UDgwwcw" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_-2AAaHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAaXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAanHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAa3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_-*********************"/>
  <commands xmi:id="_-2AAbHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_-2ADK3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAbXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAbnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAb3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_-2ADI3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAcHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_-2AAcXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAcnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAc3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAdHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_-*********************"/>
  <commands xmi:id="_-2AAdXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAdnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAd3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAeHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAeXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_-2ADG3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAenHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.trim_trace" commandName="Export Time Selection as New Trace..." description="Create a new trace containing only the events in the currently selected time range. Only available if the trace type supports it, and if a time range is selected." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAe3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAfHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAfXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAfnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAf3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_-2ADM3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAgHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAgXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_-2ADCHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAgnHkEfCAZJ6UDgwwcw" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_-2AAg3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_-*********************"/>
  <commands xmi:id="_-2AAhHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_-*********************"/>
  <commands xmi:id="_-2AAhXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_-*********************"/>
  <commands xmi:id="_-2AAhnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAh3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_-*********************"/>
  <commands xmi:id="_-2AAiHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAiXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_-*********************"/>
  <commands xmi:id="_-2AAinHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAi3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_-*********************"/>
  <commands xmi:id="_-2AAjHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAjXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAjnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAj3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAkHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAkXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAknHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoom.selection" commandName="Zoom to selection" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAk3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAlHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAlXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAlnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAl3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAmHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAmXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_-2ADFXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAmnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_-*********************"/>
  <commands xmi:id="_-2AAm3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_-*********************"/>
  <commands xmi:id="_-2AAnHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_-2ADCnHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAnXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_-2AAnnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAn3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin" commandName="Zoom in (mouse position)" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAoHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_-2ADJ3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAoXHkEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_-2AAonHkEfCAZJ6UDgwwcw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_-2AAo3HkEfCAZJ6UDgwwcw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_-2AApHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.right" commandName="Right" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AApXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_-*********************"/>
  <commands xmi:id="_-2AApnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAp3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAqHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAqXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAqnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_-2ADKXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAq3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_-2ADJHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AArHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AArXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AArnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAr3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_-2AAsHHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage" commandName="Nuclei System Technology Homepage"/>
  <commands xmi:id="_-2AAsXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAsnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_-2ADDnHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAs3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_-2AAtHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_-2AAtXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_-2ADI3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAtnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_-2AAt3HkEfCAZJ6UDgwwcw" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAuHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAuXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_-*********************"/>
  <commands xmi:id="_-2AAunHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_-2ADEXHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AAu3HkEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_-2AAvHHkEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_-2AAvXHkEfCAZJ6UDgwwcw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_-2AAvnHkEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_-2AAv3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.load" commandName="Load..." description="Load session(s)" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAwHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAwXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_-2ADNHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAwnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAw3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAxHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_-2ADMHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAxXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_-*********************"/>
  <commands xmi:id="_-2AAxnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAx3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAyHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAyXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_-2ADLXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAynHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_-*********************"/>
  <commands xmi:id="_-2AAy3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAzHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAzXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAznHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AAz3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA0HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA0XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_-2ADJHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA0nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_-2ADCnHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AA03HkEfCAZJ6UDgwwcw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_-2AA1HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA1XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA1nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA13HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_-2AC_nHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AA2HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_-2AA2XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA2nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA23HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA3HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA3XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA3nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_-*********************"/>
  <commands xmi:id="_-2AA33HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA4HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA4XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA4nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA43HkEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA5HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_-2ADK3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA5XHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.newProject" commandName="newProject" category="_-2ADLHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA5nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA53HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_-*********************"/>
  <commands xmi:id="_-2AA6HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA6XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA6nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA63HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA7HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA7XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_-2ADNHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA7nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA73HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_-*********************"/>
  <commands xmi:id="_-2AA8HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA8XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA8nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_-2ADAHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA83HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA9HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA9XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_-*********************"/>
  <commands xmi:id="_-2AA9nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.timegraph.bookmark" commandName="Toggle Bookmark..." category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA93HkEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA-HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_-*********************"/>
  <commands xmi:id="_-2AA-XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA-nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA-3HkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Documentation" commandName="NMSIS Documentation"/>
  <commands xmi:id="_-2AA_HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA_XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AA_nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_-*********************">
    <parameters xmi:id="_-2AA_3HkEfCAZJ6UDgwwcw" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_-2ABAHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABAXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABAnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABA3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugCore" commandName="Debug Core File" description="Debug a corefile" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABBHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_-*********************"/>
  <commands xmi:id="_-2ABBXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABBnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_-*********************"/>
  <commands xmi:id="_-2ABB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_-2ADCXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABCHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_-2ADAnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABCXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_-*********************"/>
  <commands xmi:id="_-2ABCnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.formatChangeLog" commandName="Format ChangeLog" description="Formats ChangeLog" category="_-2ADBHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABC3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_-*********************"/>
  <commands xmi:id="_-2ABDHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABDXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABDnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_-2ADGnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABD3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABEHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABEXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABEnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_-*********************"/>
  <commands xmi:id="_-2ABE3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABFHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABFXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABF3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABGHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABGXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_-2ABGnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABG3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_-*********************"/>
  <commands xmi:id="_-2ABHHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABHXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_-2ADMHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABHnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_-*********************"/>
  <commands xmi:id="_-2ABH3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABIHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_-*********************"/>
  <commands xmi:id="_-2ABIXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABI3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABJHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABJXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_-*********************"/>
  <commands xmi:id="_-2ABJnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_-*********************"/>
  <commands xmi:id="_-2ABJ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABKHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABKXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABKnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_-*********************"/>
  <commands xmi:id="_-2ABK3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABLHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_-*********************"/>
  <commands xmi:id="_-2ABLXHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide" commandName="Nuclei Studio User Guide"/>
  <commands xmi:id="_-2ABLnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_-2ADM3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABL3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_-2ADDXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABMHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_-*********************"/>
  <commands xmi:id="_-2ABMXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABMnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_-2ADMXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABM3HkEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_-2ADBnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABNHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABNXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABNnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABN3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABOHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABOXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABOnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_-*********************"/>
  <commands xmi:id="_-2ABO3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABPHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABPXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABPnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABP3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABQHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABQXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABQnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_-2ADLXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABQ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABRHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABRXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABRnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_-2ADM3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABR3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_-2ABSHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABSXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABSnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_-2ADHHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABS3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_-2ABTHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_-2ABTXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_-2ABTnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABT3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_-2ADDXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABUHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_-*********************"/>
  <commands xmi:id="_-2ABUXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABUnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_-2ADI3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABU3HkEfCAZJ6UDgwwcw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_-2ABVHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABVXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABVnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABV3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABWHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABWXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABWnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABW3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_-2ADBXHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABXHHkEfCAZJ6UDgwwcw" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_-2ABXXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABXnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABX3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_-2ADAHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABYHHkEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_-2ABYXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABYnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_-2ADJ3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABY3HkEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_-2ABZHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABZXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABZnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABZ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABaHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_-2ADLnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABaXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABanHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABa3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABbHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces..." description="Synchronize 2 or more traces" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABbXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABbnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_-*********************"/>
  <commands xmi:id="_-2ABb3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABcHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABcXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_-2ADKnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABcnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABc3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABdHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_-2ADLXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABdXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableLogger" commandName="Enable Logger" description="Enable Logger" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABdnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABd3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABeHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_-2ADAHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABeXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_-2ADJHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABenHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABe3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog2" commandName="Prepare Changelog In Editor" description="Prepares ChangeLog in an editor" category="_-2ADBHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABfHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABfXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABfnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABf3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABgHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_-*********************"/>
  <commands xmi:id="_-2ABgXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABgnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABg3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABhHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABhXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABhnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABh3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_-*********************"/>
  <commands xmi:id="_-2ABiHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_-*********************"/>
  <commands xmi:id="_-2ABiXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_-2ADLnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABinHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABi3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABjHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABjXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABjnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABj3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_-*********************"/>
  <commands xmi:id="_-2ABkHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_-*********************"/>
  <commands xmi:id="_-2ABkXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_-2ADK3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABknHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABk3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABlHHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands" commandName="RISC-V Project Convert Tool" category="_-2ADJnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABlXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABlnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABl3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_-2ADMnHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABmHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_-2ABmXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABmnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_-*********************"/>
  <commands xmi:id="_-2ABm3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABnHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABnXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABnnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_-*********************"/>
  <commands xmi:id="_-2ABn3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABoHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABoXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABonHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_-2ADB3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABo3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABpHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABpXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABpnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_-*********************"/>
  <commands xmi:id="_-2ABp3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_-2ADM3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABqHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABqXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABqnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABq3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABrHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABrXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABrnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABr3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABsHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_-2ADMHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABsXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_-2ADLXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABsnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABs3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABtHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABtXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABtnHkEfCAZJ6UDgwwcw" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABt3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABuHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABuXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_-*********************"/>
  <commands xmi:id="_-2ABunHkEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABu3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABvHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_-2ADC3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABvXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_-2ADGnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABvnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_-*********************"/>
  <commands xmi:id="_-2ABv3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABwHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABwXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABwnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_-2ADMXHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ABw3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_-2ABxHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_-2ABxXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABxnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABx3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AByHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AByXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_-*********************"/>
  <commands xmi:id="_-2ABynHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABy3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABzHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABzXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ABznHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_FAQ" commandName="Nuclei Studio FAQ"/>
  <commands xmi:id="_-2ABz3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_-2ADKHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB0HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB0XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB0nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB03HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB1HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB1XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB1nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_-*********************"/>
  <commands xmi:id="_-2AB13HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_-2ADMnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB2HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_-2ADCHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB2XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB2nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB23HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB3HHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" commandName="Export to Package Management" category="_-2ADEHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB3XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB3nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB33HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB4HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB4XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB4nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB43HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB5HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB5XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.openLaunchSelector" commandName="Open Launch Bar Config Selector" category="_-2ADLnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB5nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB53HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB6HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB6XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB6nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB63HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB7HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB7XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB7nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_-2ADEXHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2AB73HkEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_-2AB8HHkEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_-2AB8XHkEfCAZJ6UDgwwcw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_-2AB8nHkEfCAZJ6UDgwwcw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_-2AB83HkEfCAZJ6UDgwwcw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_-2AB9HHkEfCAZJ6UDgwwcw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_-2AB9XHkEfCAZJ6UDgwwcw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_-2AB9nHkEfCAZJ6UDgwwcw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_-2AB93HkEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_-2AB-HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB-XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB-nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.showPerspectiveCommand" commandName="Switch to CMSIS Packs Perspective" category="_-2ADDHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB-3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB_HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB_XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB_nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AB_3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_-2ADGnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACAHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACAXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_-*********************"/>
  <commands xmi:id="_-2ACAnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACA3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACBHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACBXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_-*********************"/>
  <commands xmi:id="_-2ACBnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_-*********************"/>
  <commands xmi:id="_-2ACB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACCHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACCXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACCnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACC3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_-2ACDHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_-2ACDXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACDnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACD3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_-2ADGnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACEHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_-*********************"/>
  <commands xmi:id="_-2ACEXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACEnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_-2ADNHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACE3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACFHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACFXHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sampleCommand" commandName="SDK Configuration Tools" category="_-2ADLHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACF3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_-2ADFHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACGHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_-2ACGXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACGnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_-2ADIHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACG3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACHHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_-*********************"/>
  <commands xmi:id="_-2ACHXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACHnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACH3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_-2ADAHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACIHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACIXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_-2ADG3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACI3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_-*********************"/>
  <commands xmi:id="_-2ACJHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACJXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_-*********************"/>
  <commands xmi:id="_-2ACJnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_-2ADK3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACJ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.convert_project" commandName="Configure or convert to Tracing Project" description="Configure or convert project to tracing project" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACKHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACKXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACKnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACK3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACLHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACLXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACLnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_-2ADAnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACL3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACMHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACMXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACMnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_-2ADJHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACM3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_-*********************"/>
  <commands xmi:id="_-2ACNHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACNXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_-2ADFnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACNnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACN3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACOHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACOXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACOnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_-*********************"/>
  <commands xmi:id="_-2ACO3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACPHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACPXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_-2ADBXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACPnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACP3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACQHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACQXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_-*********************"/>
  <commands xmi:id="_-2ACQnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_-2ADGnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACQ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_-2ADMHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACRHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACRXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACRnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACR3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACSHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACSXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACSnHkEfCAZJ6UDgwwcw" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_-2ACS3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACTHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACTXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_-2ADCnHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACTnHkEfCAZJ6UDgwwcw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_-2ACT3HkEfCAZJ6UDgwwcw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_-2ACUHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACUXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACUnHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManageCommand" commandName="NPK Package Management" category="_-2ADE3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACU3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_-2ADJ3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACVHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_-*********************"/>
  <commands xmi:id="_-2ACVXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_-2ADFnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACVnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_-2ADI3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACV3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACWHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACWXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACWnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_-*********************"/>
  <commands xmi:id="_-2ACW3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_-2ADCHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACXHHkEfCAZJ6UDgwwcw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_-2ACXXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACXnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_-2ADAHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACX3HkEfCAZJ6UDgwwcw" elementId="kind" name="Kind"/>
    <parameters xmi:id="_-2ACYHHkEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_-2ACYXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACYnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_-2ADAHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACY3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACZHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACZXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACZnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACZ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_-2ADA3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACaHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACaXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_-*********************"/>
  <commands xmi:id="_-2ACanHkEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACa3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACbHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_-2ADGHHkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACbXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_-2ACbnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_-2ACb3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_-2ADJ3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACcHHkEfCAZJ6UDgwwcw" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_-2ACcXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_remove" commandName="Remove External Analysis" description="Remove External Analysis" category="_-2ADFHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACcnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_-2ADJXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACc3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACdHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACdXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACdnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACd3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACeHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACeXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACenHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_-2ADD3HkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACe3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_-2ADAXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACfHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_-2AC_nHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACfXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_-2ADCnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACfnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACf3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACgHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_-2ADI3HkEfCAZJ6UDgwwcw">
    <parameters xmi:id="_-2ACgXHkEfCAZJ6UDgwwcw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_-2ACgnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_-2ADDnHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACg3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_-2ADHXHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AChHHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convertall.commands" commandName="org.riscvstudio.ide.project.convertall.commands"/>
  <commands xmi:id="_-2AChXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AChnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACh3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACiHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACiXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACinHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACi3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACjHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACjXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACjnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACj3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACkHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACkXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACknHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACk3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AClHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AClXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AClnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACl3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACmHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACmXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACmnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACm3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACnHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACnXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACnnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACn3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACoHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACoXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AConHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACo3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACpHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACpXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACpnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACp3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACqHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACqXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACqnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACq3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACrHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACrXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACrnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACr3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACsHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACsXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACsnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACs3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACtHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACtXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACtnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACt3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACuHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACuXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACunHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACu3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACvHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACvXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACvnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACv3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACwHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACwXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACwnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACw3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACxHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACxXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACxnHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACx3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACyHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACyXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACynHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACy3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACzHHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACzXHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACznHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.cdt.ui.cview.contribution/org.eclipse.mylyn.cdt.ui.cview.focusActiveTask.action" commandName="Focus on Active Task" description="Focus only on elements in active Mylyn task" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2ACz3HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC0HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC0XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC0nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC03HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC1HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC1XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC1nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC13HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC2HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC2XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC2nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC23HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC3HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC3XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC3nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC33HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC4HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC4XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC4nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC43HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC5HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC5XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC5nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC53HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC6HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC6XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC6nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC63HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC7HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC7XHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC7nHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC73HkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_-2AC8HHkEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_-2ADGHHkEfCAZJ6UDgwwcw"/>
  <addons xmi:id="_-2AC8XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_-2AC8nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_-2AC83HkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_-2AC9HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_-2AC9XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_-2AC9nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_-2AC93HkEfCAZJ6UDgwwcw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_-2AC-HHkEfCAZJ6UDgwwcw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_-2AC-XHkEfCAZJ6UDgwwcw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_-2AC-nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_-2AC-3HkEfCAZJ6UDgwwcw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_-2AC_HHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_-2AC_XHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_-2AC_nHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_-2AC_3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_-2ADAHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_-2ADAXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_-2ADAnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_-2ADA3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_-2ADBHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog" name="Changelog" description="Changelog key bindings"/>
  <categories xmi:id="_-2ADBXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_-2ADBnHkEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_-2ADB3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.commands" name="CDT Context" description="CDT Task-Focused Interface Commands"/>
  <categories xmi:id="_-2ADCHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_-2ADCXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_-2ADCnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_-2ADC3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_-2ADDHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_-2ADDXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_-2ADDnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_-2ADD3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_-2ADEHHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" name="Export to Package Management"/>
  <categories xmi:id="_-2ADEXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_-2ADEnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_-2ADE3HkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManage" name="NPK Package Management"/>
  <categories xmi:id="_-2ADFHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_-2ADFXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_-2ADFnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_-2ADF3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_-2ADGHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_-2ADGXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_-2ADGnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
  <categories xmi:id="_-2ADG3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_-2ADHHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_-2ADHXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_-2ADHnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_-2ADH3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_-2ADIHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_-2ADIXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_-2ADInHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_-2ADI3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_-2ADJHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_-2ADJXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_-2ADJnHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands.category" name="RISC-V Project Convert Tool"/>
  <categories xmi:id="_-2ADJ3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_-2ADKHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_-2ADKXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_-2ADKnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_-2ADK3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_-2ADLHHkEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.category" name="SDK Configuration Tools"/>
  <categories xmi:id="_-2ADLXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_-2ADLnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_-*********************" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_-2ADMHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_-2ADMXHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_-2ADMnHkEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_-2ADM3HkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_-2ADNHHkEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
</application:Application>
